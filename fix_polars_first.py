#!/usr/bin/env python3
"""
Script to fix all .first() calls in strategy_generators.py to use proper Polars syntax
"""

import re

def fix_polars_first_calls():
    file_path = "agents/strategy_generation/strategy_generators.py"
    
    # Read the file
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Pattern to match .first() calls
    pattern = r'(\w+) = option_chain\.filter\(\s*\(pl\.col\(\'option_type\'\) == \'[CP]E\'\) &\s*\(pl\.col\(\'strike_price\'\) == \w+\)\s*\)\.first\(\)'
    
    # Find all matches
    matches = re.findall(pattern, content)
    print(f"Found {len(matches)} .first() calls to fix")
    
    # Replace .first() with proper Polars syntax
    # Pattern 1: variable = option_chain.filter(...).first()
    content = re.sub(
        r'(\w+) = option_chain\.filter\(\s*\(pl\.col\(\'option_type\'\) == \'([CP]E)\'\) &\s*\(pl\.col\(\'strike_price\'\) == (\w+)\)\s*\)\.first\(\)',
        r'\1_df = option_chain.filter(\n                    (pl.col(\'option_type\') == \'\2\') &\n                    (pl.col(\'strike_price\') == \3)\n                )',
        content
    )
    
    # Replace the None checks
    content = re.sub(
        r'if (\w+) is None:\s*continue\s*(\w+)_dict = \1\.to_dict\(\)',
        r'if \1_df.height == 0:\n                    continue\n                \n                \2_dict = \1_df.row(0, named=True)',
        content
    )
    
    # Write back to file
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("Fixed all .first() calls")

if __name__ == "__main__":
    fix_polars_first_calls()
