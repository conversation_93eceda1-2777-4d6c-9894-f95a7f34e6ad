import logging
import polars as pl
from datetime import datetime
from typing import Dict, List, Any
from agents.strategy_generation.data_models import OptionsLeg, OptionsStrategy, StrategyType
from agents.strategy_generation.strategy_calculators import StrategyCalculators

logger = logging.getLogger(__name__)

class StrategyGenerators:
    """Generates various options strategies."""

    def __init__(self, config: Dict, calculators: StrategyCalculators):
        self.config = config
        self.calculators = calculators

    async def generate_strategy_type(self, strategy_type: StrategyType, underlying: str,
                                    option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate strategies of specific type."""
        try:
            strategies = []

            # Basic directional buying strategies
            if strategy_type == StrategyType.LONG_CALL:
                strategies = await self._generate_long_call_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.LONG_PUT:
                strategies = await self._generate_long_put_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.PROTECTIVE_PUT:
                strategies = await self._generate_protective_put_strategies(underlying, option_chain, spot_price)

            # ATM buying strategies
            elif strategy_type == StrategyType.ATM_LONG_CALL:
                strategies = await self._generate_atm_long_call_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.ATM_LONG_PUT:
                strategies = await self._generate_atm_long_put_strategies(underlying, option_chain, spot_price)

            # OTM buying strategies
            elif strategy_type == StrategyType.OTM_LONG_CALL:
                strategies = await self._generate_otm_long_call_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.OTM_LONG_PUT:
                strategies = await self._generate_otm_long_put_strategies(underlying, option_chain, spot_price)

            # Far OTM buying strategies
            elif strategy_type == StrategyType.FAR_OTM_LONG_CALL:
                strategies = await self._generate_far_otm_long_call_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.FAR_OTM_LONG_PUT:
                strategies = await self._generate_far_otm_long_put_strategies(underlying, option_chain, spot_price)

            # Intraday buying strategies
            elif strategy_type == StrategyType.INTRADAY_SCALPING_CALL:
                strategies = await self._generate_intraday_scalping_call_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.INTRADAY_SCALPING_PUT:
                strategies = await self._generate_intraday_scalping_put_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.INTRADAY_MOMENTUM_CALL:
                strategies = await self._generate_intraday_momentum_call_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.INTRADAY_MOMENTUM_PUT:
                strategies = await self._generate_intraday_momentum_put_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.INTRADAY_REVERSAL_CALL:
                strategies = await self._generate_intraday_reversal_call_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.INTRADAY_REVERSAL_PUT:
                strategies = await self._generate_intraday_reversal_put_strategies(underlying, option_chain, spot_price)

            # Gamma Scalping (Long)
            elif strategy_type == StrategyType.GAMMA_SCALPING_LONG:
                strategies = await self._generate_gamma_scalping_long_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.DELTA_NEUTRAL_GAMMA_SCALP:
                strategies = await self._generate_delta_neutral_gamma_scalp_strategies(underlying, option_chain, spot_price)

            # Volatility Breakout (Long)
            elif strategy_type == StrategyType.VOLATILITY_BREAKOUT_LONG:
                strategies = await self._generate_volatility_breakout_long_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.VIX_BASED_STRATEGY:
                strategies = await self._generate_vix_based_strategy_strategies(underlying, option_chain, spot_price)

            # Volatility buying strategies
            elif strategy_type == StrategyType.LONG_STRADDLE:
                strategies = await self._generate_long_straddle_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.LONG_STRANGLE:
                strategies = await self._generate_long_strangle_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.REVERSE_IRON_CONDOR:
                strategies = await self._generate_reverse_iron_condor_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.REVERSE_IRON_BUTTERFLY:
                strategies = await self._generate_reverse_iron_butterfly_strategies(underlying, option_chain, spot_price)

            # Spread buying strategies (net debit spreads)
            elif strategy_type == StrategyType.BULL_CALL_SPREAD:
                strategies = await self._generate_bull_call_spread_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.BEAR_PUT_SPREAD:
                strategies = await self._generate_bear_put_spread_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.CALL_CALENDAR_SPREAD:
                strategies = await self._generate_call_calendar_spread_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.PUT_CALENDAR_SPREAD:
                strategies = await self._generate_put_calendar_spread_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.DIAGONAL_CALL_SPREAD:
                strategies = await self._generate_diagonal_call_spread_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.DIAGONAL_PUT_SPREAD:
                strategies = await self._generate_diagonal_put_spread_strategies(underlying, option_chain, spot_price)

            # Ratio buying strategies (backspreads are net debit)
            elif strategy_type == StrategyType.RATIO_CALL_BACKSPREAD:
                strategies = await self._generate_ratio_call_backspread_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.RATIO_PUT_BACKSPREAD:
                strategies = await self._generate_ratio_put_backspread_strategies(underlying, option_chain, spot_price)

            # Collar strategies (often net zero or small debit/credit)
            elif strategy_type == StrategyType.COLLAR:
                strategies = await self._generate_collar_strategies(underlying, option_chain, spot_price)

            # Synthetic buying strategies
            elif strategy_type == StrategyType.SYNTHETIC_LONG:
                strategies = await self._generate_synthetic_long_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.SYNTHETIC_CALL:
                strategies = await self._generate_synthetic_call_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.SYNTHETIC_PUT:
                strategies = await self._generate_synthetic_put_strategies(underlying, option_chain, spot_price)

            # Complex Multi-leg buying strategies
            elif strategy_type == StrategyType.BUTTERFLY_SPREAD:
                strategies = await self._generate_butterfly_spread_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.CONDOR_SPREAD:
                strategies = await self._generate_condor_spread_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.CHRISTMAS_TREE:
                strategies = await self._generate_christmas_tree_strategies(underlying, option_chain, spot_price)

            # Indian market specific buying strategies
            elif strategy_type == StrategyType.WEEKLY_EXPIRY_STRADDLE:
                strategies = await self._generate_weekly_expiry_straddle_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.MONTHLY_EXPIRY_STRANGLE:
                strategies = await self._generate_monthly_expiry_strangle_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.BANKNIFTY_BUTTERFLY:
                strategies = await self._generate_banknifty_butterfly_strategies(underlying, option_chain, spot_price)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate {strategy_type.value} strategies: {e}")
            return []
    
    async def _generate_long_call_strategies(self, underlying: str, option_chain: pl.DataFrame, 
                                           spot_price: float) -> List[OptionsStrategy]:
        """Generate long call strategies"""
        try:
            strategies = []
            
            # Get call options
            calls = option_chain.filter(pl.col('option_type') == 'CE')
            
            for call_row in calls.iter_rows(named=True):
                # Create single leg strategy
                leg = OptionsLeg(
                    symbol=call_row['symbol'],
                    option_type='CE',
                    strike_price=call_row['strike_price'],
                    expiry_date='2024-01-25',  # Sample expiry
                    quantity=1,
                    premium=call_row['ltp'],
                    underlying=underlying
                )
                
                # Calculate strategy metrics
                max_profit = float('inf')  # Unlimited upside
                max_loss = call_row['ltp']
                break_even = call_row['strike_price'] + call_row['ltp']
                
                # Estimate probability of profit (simplified)
                prob_profit = max(0.0, min(1.0, (spot_price - break_even) / spot_price + 0.5))
                
                strategy = OptionsStrategy(
                    strategy_id=f"LC_{underlying}_{call_row['strike_price']}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.LONG_CALL,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-call_row['ltp'],  # Negative because we pay premium
                    margin_required=call_row['ltp'],
                    risk_reward_ratio=float('inf') if max_loss > 0 else 0,
                    target_profit=max_loss * 2,  # 200% return target
                    stop_loss=max_loss * 0.5,  # 50% stop loss
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Price", "operator": "break_above", "value": spot_price * 1.01}],
                    exit_conditions=[{"indicator": "Price", "operator": "fall_below", "value": spot_price * 0.99}]
                )
                
                strategies.append(strategy)
            
            return strategies
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate long call strategies: {e}")
            return []
    
    async def _generate_long_put_strategies(self, underlying: str, option_chain: pl.DataFrame, 
                                          spot_price: float) -> List[OptionsStrategy]:
        """Generate long put strategies"""
        try:
            strategies = []
            
            # Get put options
            puts = option_chain.filter(pl.col('option_type') == 'PE')
            
            for put_row in puts.iter_rows(named=True):
                # Create single leg strategy
                leg = OptionsLeg(
                    symbol=put_row['symbol'],
                    option_type='PE',
                    strike_price=put_row['strike_price'],
                    expiry_date='2024-01-25',  # Sample expiry
                    quantity=1,
                    premium=put_row['ltp'],
                    underlying=underlying
                )
                
                # Calculate strategy metrics
                max_profit = put_row['strike_price'] - put_row['ltp']
                max_loss = put_row['ltp']
                break_even = put_row['strike_price'] - put_row['ltp']
                
                # Estimate probability of profit
                prob_profit = max(0.0, min(1.0, (break_even - spot_price) / spot_price + 0.5))
                
                strategy = OptionsStrategy(
                    strategy_id=f"LP_{underlying}_{put_row['strike_price']}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.LONG_PUT,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-put_row['ltp'],
                    margin_required=put_row['ltp'],
                    risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0,
                    target_profit=max_loss * 2,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "RSI", "operator": "<", "value": 30}], # Placeholder
                    exit_conditions=[{"indicator": "RSI", "operator": ">", "value": 70}] # Placeholder
                )
                
                strategies.append(strategy)
            
            return strategies
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate long put strategies: {e}")
            return []
    
    async def _generate_long_straddle_strategies(self, underlying: str, option_chain: pl.DataFrame, 
                                               spot_price: float) -> List[OptionsStrategy]:
        """Generate long straddle strategies"""
        try:
            strategies = []
            
            # Get unique strikes
            strikes = option_chain['strike_price'].unique().sort().to_list()
            
            for strike in strikes:
                # Get call and put at same strike
                call = option_chain.filter(
                    (pl.col('option_type') == 'CE') & 
                    (pl.col('strike_price') == strike)
                ).row(0, named=True) if option_chain.filter(
                    (pl.col('option_type') == 'CE') & 
                    (pl.col('strike_price') == strike)
                ).height > 0 else None
                
                put = option_chain.filter(
                    (pl.col('option_type') == 'PE') & 
                    (pl.col('strike_price') == strike)
                ).row(0, named=True) if option_chain.filter(
                    (pl.col('option_type') == 'PE') & 
                    (pl.col('strike_price') == strike)
                ).height > 0 else None
                
                if not call or not put:
                    continue
                
                # Create legs
                call_leg = OptionsLeg(
                    symbol=call['symbol'],
                    option_type='CE',
                    strike_price=strike,
                    expiry_date='2024-01-25',
                    quantity=1,
                    premium=call['ltp'],
                    underlying=underlying
                )
                
                put_leg = OptionsLeg(
                    symbol=put['symbol'],
                    option_type='PE',
                    strike_price=strike,
                    expiry_date='2024-01-25',
                    quantity=1,
                    premium=put['ltp'],
                    underlying=underlying
                )
                
                # Calculate strategy metrics
                total_premium = call['ltp'] + put['ltp']
                max_profit = float('inf')  # Unlimited if big move
                max_loss = total_premium
                break_even_upper = strike + total_premium
                break_even_lower = strike - total_premium
                
                # Estimate probability of profit (move beyond break-evens)
                prob_profit = 0.4  # Simplified estimate for volatility strategy
                
                strategy = OptionsStrategy(
                    strategy_id=f"LS_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.LONG_STRADDLE,
                    underlying=underlying,
                    legs=[call_leg, put_leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even_lower, break_even_upper],
                    probability_of_profit=prob_profit,
                    net_premium=-total_premium,
                    margin_required=total_premium,
                    risk_reward_ratio=float('inf'),
                    target_profit=max_loss * 2,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "IV", "operator": "low"}], # Placeholder
                    exit_conditions=[{"indicator": "IV", "operator": "high"}] # Placeholder
                )
                
                strategies.append(strategy)
            
            return strategies
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate long straddle strategies: {e}")
            return []
    
    async def _generate_long_strangle_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                               spot_price: float) -> List[OptionsStrategy]:
        """Generate long strangle strategies"""
        try:
            strategies = []

            # Get OTM strikes for calls and puts
            otm_call_strikes = self.calculators.get_otm_strikes(option_chain, spot_price, 'CE')
            otm_put_strikes = self.calculators.get_otm_strikes(option_chain, spot_price, 'PE')

            for call_strike in otm_call_strikes[:3]:  # Limit to top 3
                for put_strike in otm_put_strikes[:3]:
                    if call_strike <= put_strike:
                        continue

                    # Get call and put options
                    call = option_chain.filter(
                        (pl.col('option_type') == 'CE') &
                        (pl.col('strike_price') == call_strike)
                    ).row(0, named=True) if option_chain.filter(
                        (pl.col('option_type') == 'CE') &
                        (pl.col('strike_price') == call_strike)
                    ).height > 0 else None

                    put = option_chain.filter(
                        (pl.col('option_type') == 'PE') &
                        (pl.col('strike_price') == put_strike)
                    ).row(0, named=True) if option_chain.filter(
                        (pl.col('option_type') == 'PE') &
                        (pl.col('strike_price') == put_strike)
                    ).height > 0 else None

                    if not call or not put:
                        continue

                    # Create legs
                    call_leg = OptionsLeg(
                        symbol=call['symbol'],
                        option_type='CE',
                        strike_price=call_strike,
                        expiry_date='2024-01-25',
                        quantity=1,
                        premium=call['ltp'],
                        underlying=underlying
                    )

                    put_leg = OptionsLeg(
                        symbol=put['symbol'],
                        option_type='PE',
                        strike_price=put_strike,
                        expiry_date='2024-01-25',
                        quantity=1,
                        premium=put['ltp'],
                        underlying=underlying
                    )

                    # Calculate strategy metrics
                    total_premium = call['ltp'] + put['ltp']
                    max_profit = float('inf')  # Unlimited if big move
                    max_loss = total_premium
                    break_even_upper = call_strike + total_premium
                    break_even_lower = put_strike - total_premium

                    # Estimate probability of profit
                    prob_profit = 0.35  # Lower than straddle due to wider strikes

                    strategy = OptionsStrategy(
                        strategy_id=f"LST_{underlying}_{call_strike}_{put_strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                        strategy_type=StrategyType.LONG_STRANGLE,
                        underlying=underlying,
                        legs=[call_leg, put_leg],
                        max_profit=max_profit,
                        max_loss=max_loss,
                        break_even_points=[break_even_lower, break_even_upper],
                        probability_of_profit=prob_profit,
                        net_premium=-total_premium,
                        margin_required=total_premium,
                        risk_reward_ratio=float('inf'),
                        target_profit=max_loss * 2,
                        stop_loss=max_loss * 0.5,
                        created_at=datetime.now(),
                        entry_conditions=[{"indicator": "IV", "operator": "low"}], # Placeholder
                        exit_conditions=[{"indicator": "IV", "operator": "high"}] # Placeholder
                    )

                    strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate long strangle strategies: {e}")
            return []
    
    # ATM Strategy Methods
    async def _generate_atm_long_call_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                               spot_price: float) -> List[OptionsStrategy]:
        """Generate ATM long call strategies"""
        try:
            strategies = []
            atm_strikes = self.calculators.get_atm_strikes(option_chain, spot_price)

            for strike in atm_strikes:
                call = option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                ).row(0, named=True) if option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                ).height > 0 else None

                if not call:
                    continue

                leg = OptionsLeg(
                    symbol=call['symbol'],
                    option_type='CE',
                    strike_price=strike,
                    expiry_date='2024-01-25',
                    quantity=1,
                    premium=call['ltp'],
                    underlying=underlying
                )

                max_profit = float('inf')
                max_loss = call['ltp']
                break_even = strike + call['ltp']
                prob_profit = 0.5  # ATM has 50% probability

                strategy = OptionsStrategy(
                    strategy_id=f"ATMLC_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.ATM_LONG_CALL,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-call['ltp'],
                    margin_required=call['ltp'],
                    risk_reward_ratio=float('inf'),
                    target_profit=max_loss * 2,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Volume", "operator": ">", "value": 10000}], # Placeholder
                    exit_conditions=[{"indicator": "Time", "operator": "end_of_day"}] # Placeholder
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate ATM long call strategies: {e}")
            return []

    async def _generate_atm_long_put_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                              spot_price: float) -> List[OptionsStrategy]:
        """Generate ATM long put strategies"""
        try:
            strategies = []
            atm_strikes = self.calculators.get_atm_strikes(option_chain, spot_price)

            for strike in atm_strikes:
                put = option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                ).row(0, named=True) if option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                ).height > 0 else None

                if not put:
                    continue

                leg = OptionsLeg(
                    symbol=put['symbol'],
                    option_type='PE',
                    strike_price=strike,
                    expiry_date='2024-01-25',
                    quantity=1,
                    premium=put['ltp'],
                    underlying=underlying
                )

                max_profit = strike - put['ltp']
                max_loss = put['ltp']
                break_even = strike - put['ltp']
                prob_profit = 0.5  # ATM has 50% probability

                strategy = OptionsStrategy(
                    strategy_id=f"ATMLP_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.ATM_LONG_PUT,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-put['ltp'],
                    margin_required=put['ltp'],
                    risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0,
                    target_profit=max_loss * 2,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Volume", "operator": ">", "value": 10000}], # Placeholder
                    exit_conditions=[{"indicator": "Time", "operator": "end_of_day"}] # Placeholder
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate ATM long put strategies: {e}")
            return []

    async def _generate_atm_short_call_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                                spot_price: float) -> List[OptionsStrategy]:
        """Generate ATM short call strategies"""
        try:
            strategies = []
            atm_strikes = self.calculators.get_atm_strikes(option_chain, spot_price)

            for strike in atm_strikes:
                call = option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                ).row(0, named=True) if option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                ).height > 0 else None

                if not call:
                    continue

                leg = OptionsLeg(
                    symbol=call['symbol'],
                    option_type='CE',
                    strike_price=strike,
                    expiry_date='2024-01-25',
                    quantity=-1,  # Short position
                    premium=call['ltp'],
                    underlying=underlying
                )

                max_profit = call['ltp']
                max_loss = float('inf')  # Unlimited loss
                break_even = strike + call['ltp']
                prob_profit = 0.5  # ATM has 50% probability

                strategy = OptionsStrategy(
                    strategy_id=f"ATMSC_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.ATM_SHORT_CALL,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=call['ltp'],  # Positive because we receive premium
                    margin_required=call['ltp'] * 10,  # Estimated margin
                    risk_reward_ratio=0,  # Unlimited loss
                    target_profit=max_profit * 0.8,  # 80% of max profit
                    stop_loss=max_profit * 2,  # 200% of premium received
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Delta", "operator": "near_zero"}], # Placeholder
                    exit_conditions=[{"indicator": "Delta", "operator": "far_from_zero"}] # Placeholder
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate ATM short call strategies: {e}")
            return []

    async def _generate_atm_short_put_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                                spot_price: float) -> List[OptionsStrategy]:
        """Generate ATM short put strategies"""
        try:
            strategies = []
            atm_strikes = self.calculators.get_atm_strikes(option_chain, spot_price)

            for strike in atm_strikes:
                put = option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                ).row(0, named=True) if option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                ).height > 0 else None

                if not put:
                    continue

                leg = OptionsLeg(
                    symbol=put['symbol'],
                    option_type='PE',
                    strike_price=strike,
                    expiry_date='2024-01-25',
                    quantity=-1,  # Short position
                    premium=put['ltp'],
                    underlying=underlying
                )

                max_profit = put['ltp']
                max_loss = strike - put['ltp']
                break_even = strike - put['ltp']
                prob_profit = 0.5  # ATM has 50% probability

                strategy = OptionsStrategy(
                    strategy_id=f"ATMSP_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.ATM_SHORT_PUT,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=put['ltp'],  # Positive because we receive premium
                    margin_required=put['ltp'] * 10,  # Estimated margin
                    risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0,
                    target_profit=max_profit * 0.8,  # 80% of max profit
                    stop_loss=max_profit * 2,  # 200% of premium received
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Delta", "operator": "near_zero"}], # Placeholder
                    exit_conditions=[{"indicator": "Delta", "operator": "far_from_zero"}] # Placeholder
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate ATM short put strategies: {e}")
            return []

    async def _generate_bull_call_spread_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                                  spot_price: float) -> List[OptionsStrategy]:
        """Generate bull call spread strategies (net debit)"""
        try:
            strategies = []
            calls = option_chain.filter(pl.col('option_type') == 'CE').sort('strike_price')

            # Group calls by expiry date
            for expiry_date in calls['expiry_date'].unique().to_list():
                expiry_calls = calls.filter(pl.col('expiry_date') == expiry_date)

                if expiry_calls.height < 2:
                    continue

                # Iterate through possible combinations for long and short calls
                for i in range(expiry_calls.height):
                    long_call_row = expiry_calls.row(i, named=True)
                    for j in range(i + 1, expiry_calls.height):
                        short_call_row = expiry_calls.row(j, named=True)

                        # Ensure long call strike is lower than short call strike
                        if long_call_row['strike_price'] >= short_call_row['strike_price']:
                            continue

                        # Create legs
                        long_leg = OptionsLeg(
                            symbol=long_call_row['symbol'],
                            option_type='CE',
                            strike_price=long_call_row['strike_price'],
                            expiry_date=expiry_date,
                            quantity=1,
                            premium=long_call_row['ltp'],
                            underlying=underlying
                        )

                        short_leg = OptionsLeg(
                            symbol=short_call_row['symbol'],
                            option_type='CE',
                            strike_price=short_call_row['strike_price'],
                            expiry_date=expiry_date,
                            quantity=-1,  # Short position
                            premium=short_call_row['ltp'],
                            underlying=underlying
                        )

                        # Calculate strategy metrics
                        net_premium = long_call_row['ltp'] - short_call_row['ltp']
                        if net_premium <= 0:  # Must be a debit spread
                            continue

                        max_profit = (short_call_row['strike_price'] - long_call_row['strike_price']) - net_premium
                        max_loss = net_premium
                        break_even = long_call_row['strike_price'] + net_premium

                        # Estimate probability of profit (simplified)
                        prob_profit = max(0.0, min(1.0, (spot_price - break_even) / (short_call_row['strike_price'] - long_call_row['strike_price']) + 0.5))

                        strategy = OptionsStrategy(
                            strategy_id=f"BCS_{underlying}_{long_call_row['strike_price']}_{short_call_row['strike_price']}_{expiry_date}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                            strategy_type=StrategyType.BULL_CALL_SPREAD,
                            underlying=underlying,
                            legs=[long_leg, short_leg],
                            max_profit=max_profit,
                            max_loss=max_loss,
                            break_even_points=[break_even],
                            probability_of_profit=prob_profit,
                            net_premium=-net_premium,  # Negative for debit
                            margin_required=max_loss,  # Margin is max loss for debit spreads
                            risk_reward_ratio=max_profit / max_loss if max_loss > 0 else float('inf'),
                            target_profit=max_profit * 0.8,
                            stop_loss=max_loss * 0.5,
                            created_at=datetime.now(),
                            entry_conditions=[{"indicator": "Price", "operator": "above_support", "value": spot_price * 0.98}],
                            exit_conditions=[{"indicator": "Price", "operator": "below_resistance", "value": spot_price * 1.05}]
                        )
                        strategies.append(strategy)
            return strategies
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate bull call spread strategies: {e}")
            return []
    
    async def _generate_bear_put_spread_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                                 spot_price: float) -> List[OptionsStrategy]:
        """Generate bear put spread strategies (net debit)"""
        try:
            strategies = []
            puts = option_chain.filter(pl.col('option_type') == 'PE').sort('strike_price', descending=True)

            # Group puts by expiry date
            for expiry_date in puts['expiry_date'].unique().to_list():
                expiry_puts = puts.filter(pl.col('expiry_date') == expiry_date)

                if expiry_puts.height < 2:
                    continue

                # Iterate through possible combinations for long and short puts
                for i in range(expiry_puts.height):
                    long_put_row = expiry_puts.row(i, named=True)
                    for j in range(i + 1, expiry_puts.height):
                        short_put_row = expiry_puts.row(j, named=True)

                        # Ensure long put strike is higher than short put strike
                        if long_put_row['strike_price'] <= short_put_row['strike_price']:
                            continue

                        # Create legs
                        long_leg = OptionsLeg(
                            symbol=long_put_row['symbol'],
                            option_type='PE',
                            strike_price=long_put_row['strike_price'],
                            expiry_date=expiry_date,
                            quantity=1,
                            premium=long_put_row['ltp'],
                            underlying=underlying
                        )

                        short_leg = OptionsLeg(
                            symbol=short_put_row['symbol'],
                            option_type='PE',
                            strike_price=short_put_row['strike_price'],
                            expiry_date=expiry_date,
                            quantity=-1,  # Short position
                            premium=short_put_row['ltp'],
                            underlying=underlying
                        )

                        # Calculate strategy metrics
                        net_premium = long_put_row['ltp'] - short_put_row['ltp']
                        if net_premium <= 0:  # Must be a debit spread
                            continue

                        max_profit = (long_put_row['strike_price'] - short_put_row['strike_price']) - net_premium
                        max_loss = net_premium
                        break_even = long_put_row['strike_price'] - net_premium

                        # Estimate probability of profit (simplified)
                        prob_profit = max(0.0, min(1.0, (break_even - spot_price) / (long_put_row['strike_price'] - short_put_row['strike_price']) + 0.5))

                        strategy = OptionsStrategy(
                            strategy_id=f"BPS_{underlying}_{long_put_row['strike_price']}_{short_put_row['strike_price']}_{expiry_date}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                            strategy_type=StrategyType.BEAR_PUT_SPREAD,
                            underlying=underlying,
                            legs=[long_leg, short_leg],
                            max_profit=max_profit,
                            max_loss=max_loss,
                            break_even_points=[break_even],
                            probability_of_profit=prob_profit,
                            net_premium=-net_premium,  # Negative for debit
                            margin_required=max_loss,  # Margin is max loss for debit spreads
                            risk_reward_ratio=max_profit / max_loss if max_loss > 0 else float('inf'),
                            target_profit=max_profit * 0.8,
                            stop_loss=max_loss * 0.5,
                            created_at=datetime.now(),
                            entry_conditions=[{"indicator": "Price", "operator": "below_resistance", "value": spot_price * 1.02}],
                            exit_conditions=[{"indicator": "Price", "operator": "above_support", "value": spot_price * 0.95}]
                        )
                        strategies.append(strategy)
            return strategies
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate bear put spread strategies: {e}")
            return []

    async def _generate_iron_condor_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                             spot_price: float) -> List[OptionsStrategy]:
        """Generate iron condor strategies"""
        # Four-leg strategy: sell call spread + sell put spread
        # Implementation would create four-leg strategies
        return []

    # Implemented methods for remaining strategies
    async def _generate_protective_put_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate protective put strategies"""
        try:
            strategies = []

            # Get OTM put strikes (below spot price)
            otm_put_strikes = self.calculators.get_otm_strikes(option_chain, spot_price, 'PE')

            for strike in otm_put_strikes[:5]:  # Limit to top 5
                put_df = option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                )

                if put_df.height == 0:
                    continue

                put_dict = put_df.row(0, named=True)

                # Create protective put leg
                leg = OptionsLeg(
                    symbol=f"{underlying}{put_dict['expiry_date'].replace('-', '')}{int(strike)}PE",
                    option_type='PE',
                    strike_price=strike,
                    expiry_date=put_dict['expiry_date'],
                    quantity=1,
                    premium=put_dict['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics
                max_profit = float('inf')  # Unlimited upside from stock
                max_loss = spot_price - strike + put_dict['ltp']  # Limited downside
                break_even = spot_price + put_dict['ltp']
                prob_profit = 0.6  # Higher probability due to protection

                strategy = OptionsStrategy(
                    strategy_id=f"PP_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.PROTECTIVE_PUT,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-put_dict['ltp'],
                    margin_required=put_dict['ltp'],
                    risk_reward_ratio=float('inf'),
                    target_profit=max_loss * 3,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Trend", "operator": "bullish", "value": "strong"}],
                    exit_conditions=[{"indicator": "Protection", "operator": "activated", "value": strike}]
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate protective put strategies: {e}")
            return []

    async def _generate_otm_long_call_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate OTM long call strategies"""
        try:
            strategies = []

            # Get OTM call strikes (above spot price)
            otm_call_strikes = self.calculators.get_otm_strikes(option_chain, spot_price, 'CE')

            for strike in otm_call_strikes[:10]:  # Limit to top 10
                call_df = option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                )

                if call_df.height == 0:
                    continue

                call_dict = call_df.row(0, named=True)

                # Create OTM call leg
                leg = OptionsLeg(
                    symbol=f"{underlying}{call_dict['expiry_date'].replace('-', '')}{int(strike)}CE",
                    option_type='CE',
                    strike_price=strike,
                    expiry_date=call_dict['expiry_date'],
                    quantity=1,
                    premium=call_dict['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics
                max_profit = float('inf')
                max_loss = call_dict['ltp']
                break_even = strike + call_dict['ltp']
                prob_profit = 0.4  # Lower probability for OTM

                strategy = OptionsStrategy(
                    strategy_id=f"OTMLC_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.OTM_LONG_CALL,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-call_dict['ltp'],
                    margin_required=call_dict['ltp'],
                    risk_reward_ratio=float('inf'),
                    target_profit=max_loss * 3,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Momentum", "operator": "strong_bullish", "value": 0.7}],
                    exit_conditions=[{"indicator": "Profit", "operator": "target_reached", "value": max_loss * 2}]
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate OTM long call strategies: {e}")
            return []

    async def _generate_otm_long_put_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate OTM long put strategies"""
        try:
            strategies = []

            # Get OTM put strikes (below spot price)
            otm_put_strikes = self.calculators.get_otm_strikes(option_chain, spot_price, 'PE')

            for strike in otm_put_strikes[:10]:  # Limit to top 10
                put_df = option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                )

                if put_df.height == 0:
                    continue

                put_dict = put_df.row(0, named=True)

                # Create OTM put leg
                leg = OptionsLeg(
                    symbol=f"{underlying}{put_dict['expiry_date'].replace('-', '')}{int(strike)}PE",
                    option_type='PE',
                    strike_price=strike,
                    expiry_date=put_dict['expiry_date'],
                    quantity=1,
                    premium=put_dict['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics
                max_profit = strike - put_dict['ltp']
                max_loss = put_dict['ltp']
                break_even = strike - put_dict['ltp']
                prob_profit = 0.4  # Lower probability for OTM

                strategy = OptionsStrategy(
                    strategy_id=f"OTMLP_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.OTM_LONG_PUT,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-put_dict['ltp'],
                    margin_required=put_dict['ltp'],
                    risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0,
                    target_profit=max_loss * 2,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Momentum", "operator": "strong_bearish", "value": -0.7}],
                    exit_conditions=[{"indicator": "Profit", "operator": "target_reached", "value": max_loss * 2}]
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate OTM long put strategies: {e}")
            return []

    async def _generate_far_otm_long_call_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate Far OTM long call strategies"""
        try:
            strategies = []

            # Get OTM call strikes and select far ones (higher strikes)
            otm_call_strikes = self.calculators.get_otm_strikes(option_chain, spot_price, 'CE')
            far_otm_strikes = [s for s in otm_call_strikes if s > spot_price * 1.05]  # At least 5% OTM

            for strike in far_otm_strikes[:8]:  # Limit to top 8
                call_df = option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                )

                if call_df.height == 0:
                    continue

                call_dict = call_df.row(0, named=True)

                # Create Far OTM call leg
                leg = OptionsLeg(
                    symbol=f"{underlying}{call_dict['expiry_date'].replace('-', '')}{int(strike)}CE",
                    option_type='CE',
                    strike_price=strike,
                    expiry_date=call_dict['expiry_date'],
                    quantity=1,
                    premium=call_dict['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics
                max_profit = float('inf')
                max_loss = call_dict['ltp']
                break_even = strike + call_dict['ltp']
                prob_profit = 0.25  # Lower probability for far OTM

                strategy = OptionsStrategy(
                    strategy_id=f"FOTMLC_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.FAR_OTM_LONG_CALL,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-call_dict['ltp'],
                    margin_required=call_dict['ltp'],
                    risk_reward_ratio=float('inf'),
                    target_profit=max_loss * 5,
                    stop_loss=max_loss * 0.3,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Breakout", "operator": "strong_bullish", "value": 0.8}],
                    exit_conditions=[{"indicator": "Profit", "operator": "target_reached", "value": max_loss * 3}]
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate far OTM long call strategies: {e}")
            return []

    async def _generate_far_otm_long_put_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate Far OTM long put strategies"""
        try:
            strategies = []

            # Get OTM put strikes and select far ones (lower strikes)
            otm_put_strikes = self.calculators.get_otm_strikes(option_chain, spot_price, 'PE')
            far_otm_strikes = [s for s in otm_put_strikes if s < spot_price * 0.95]  # At least 5% OTM

            for strike in far_otm_strikes[:8]:  # Limit to top 8
                put_df = option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                )

                if put_df.height == 0:
                    continue

                put_dict = put_df.row(0, named=True)

                # Create Far OTM put leg
                leg = OptionsLeg(
                    symbol=f"{underlying}{put_dict['expiry_date'].replace('-', '')}{int(strike)}PE",
                    option_type='PE',
                    strike_price=strike,
                    expiry_date=put_dict['expiry_date'],
                    quantity=1,
                    premium=put_dict['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics
                max_profit = strike - put_dict['ltp']
                max_loss = put_dict['ltp']
                break_even = strike - put_dict['ltp']
                prob_profit = 0.25  # Lower probability for far OTM

                strategy = OptionsStrategy(
                    strategy_id=f"FOTMLP_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.FAR_OTM_LONG_PUT,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-put_dict['ltp'],
                    margin_required=put_dict['ltp'],
                    risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0,
                    target_profit=max_loss * 4,
                    stop_loss=max_loss * 0.3,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Breakdown", "operator": "strong_bearish", "value": -0.8}],
                    exit_conditions=[{"indicator": "Profit", "operator": "target_reached", "value": max_loss * 3}]
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate far OTM long put strategies: {e}")
            return []

    async def _generate_deep_otm_long_call_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate Deep OTM long call strategies"""
        try:
            strategies = []

            # Get OTM call strikes and select deep ones (very high strikes)
            otm_call_strikes = self.calculators.get_otm_strikes(option_chain, spot_price, 'CE')
            deep_otm_strikes = [s for s in otm_call_strikes if s > spot_price * 1.10]  # At least 10% OTM

            for strike in deep_otm_strikes[:5]:  # Limit to top 5
                call = option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                ).first()

                if call is None:
                    continue

                call_dict = call.to_dict()

                # Create Deep OTM call leg
                leg = OptionsLeg(
                    symbol=f"{underlying}{call_dict['expiry_date'].replace('-', '')}{int(strike)}CE",
                    option_type='CE',
                    strike_price=strike,
                    expiry_date=call_dict['expiry_date'],
                    quantity=1,
                    premium=call_dict['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics
                max_profit = float('inf')
                max_loss = call_dict['ltp']
                break_even = strike + call_dict['ltp']
                prob_profit = 0.15  # Very low probability for deep OTM

                strategy = OptionsStrategy(
                    strategy_id=f"DOTMLC_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.DEEP_OTM_LONG_CALL,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-call_dict['ltp'],
                    margin_required=call_dict['ltp'],
                    risk_reward_ratio=float('inf'),
                    target_profit=max_loss * 10,
                    stop_loss=max_loss * 0.2,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Event", "operator": "major_bullish", "value": "earnings_surprise"}],
                    exit_conditions=[{"indicator": "Profit", "operator": "target_reached", "value": max_loss * 5}]
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate deep OTM long call strategies: {e}")
            return []

    async def _generate_deep_otm_long_put_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate Deep OTM long put strategies"""
        try:
            strategies = []

            # Get OTM put strikes and select deep ones (very low strikes)
            otm_put_strikes = self.calculators.get_otm_strikes(option_chain, spot_price, 'PE')
            deep_otm_strikes = [s for s in otm_put_strikes if s < spot_price * 0.90]  # At least 10% OTM

            for strike in deep_otm_strikes[:5]:  # Limit to top 5
                put = option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                ).first()

                if put is None:
                    continue

                put_dict = put.to_dict()

                # Create Deep OTM put leg
                leg = OptionsLeg(
                    symbol=f"{underlying}{put_dict['expiry_date'].replace('-', '')}{int(strike)}PE",
                    option_type='PE',
                    strike_price=strike,
                    expiry_date=put_dict['expiry_date'],
                    quantity=1,
                    premium=put_dict['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics
                max_profit = strike - put_dict['ltp']
                max_loss = put_dict['ltp']
                break_even = strike - put_dict['ltp']
                prob_profit = 0.15  # Very low probability for deep OTM

                strategy = OptionsStrategy(
                    strategy_id=f"DOTMLP_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.DEEP_OTM_LONG_PUT,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-put_dict['ltp'],
                    margin_required=put_dict['ltp'],
                    risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0,
                    target_profit=max_loss * 8,
                    stop_loss=max_loss * 0.2,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Event", "operator": "major_bearish", "value": "market_crash"}],
                    exit_conditions=[{"indicator": "Profit", "operator": "target_reached", "value": max_loss * 5}]
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate deep OTM long put strategies: {e}")
            return []

    async def _generate_intraday_scalping_call_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate intraday scalping call strategies"""
        try:
            strategies = []

            # Get ATM and near ATM call strikes for scalping
            atm_strikes = self.calculators.get_atm_strikes(option_chain, spot_price)

            for strike in atm_strikes[:3]:  # Limit to top 3 ATM strikes
                call = option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                ).first()

                if call is None:
                    continue

                call_dict = call.to_dict()

                # Create intraday scalping call leg
                leg = OptionsLeg(
                    symbol=f"{underlying}{call_dict['expiry_date'].replace('-', '')}{int(strike)}CE",
                    option_type='CE',
                    strike_price=strike,
                    expiry_date=call_dict['expiry_date'],
                    quantity=1,
                    premium=call_dict['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics for scalping
                max_profit = float('inf')
                max_loss = call_dict['ltp']
                break_even = strike + call_dict['ltp']
                prob_profit = 0.6  # Higher probability for scalping

                strategy = OptionsStrategy(
                    strategy_id=f"IDSC_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.INTRADAY_SCALPING_CALL,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-call_dict['ltp'],
                    margin_required=call_dict['ltp'],
                    risk_reward_ratio=float('inf'),
                    target_profit=max_loss * 0.3,  # Quick 30% profit target
                    stop_loss=max_loss * 0.2,     # Quick 20% stop loss
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Volume", "operator": "spike", "value": 2.0}],
                    exit_conditions=[{"indicator": "Time", "operator": "end_of_day", "value": "15:20"}]
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate intraday scalping call strategies: {e}")
            return []

    async def _generate_intraday_scalping_put_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate intraday scalping put strategies"""
        try:
            strategies = []

            # Get ATM and near ATM put strikes for scalping
            atm_strikes = self.calculators.get_atm_strikes(option_chain, spot_price)

            for strike in atm_strikes[:3]:  # Limit to top 3 ATM strikes
                put = option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                ).first()

                if put is None:
                    continue

                put_dict = put.to_dict()

                # Create intraday scalping put leg
                leg = OptionsLeg(
                    symbol=f"{underlying}{put_dict['expiry_date'].replace('-', '')}{int(strike)}PE",
                    option_type='PE',
                    strike_price=strike,
                    expiry_date=put_dict['expiry_date'],
                    quantity=1,
                    premium=put_dict['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics for scalping
                max_profit = strike - put_dict['ltp']
                max_loss = put_dict['ltp']
                break_even = strike - put_dict['ltp']
                prob_profit = 0.6  # Higher probability for scalping

                strategy = OptionsStrategy(
                    strategy_id=f"IDSP_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.INTRADAY_SCALPING_PUT,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-put_dict['ltp'],
                    margin_required=put_dict['ltp'],
                    risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0,
                    target_profit=max_loss * 0.3,  # Quick 30% profit target
                    stop_loss=max_loss * 0.2,     # Quick 20% stop loss
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Volume", "operator": "spike", "value": 2.0}],
                    exit_conditions=[{"indicator": "Time", "operator": "end_of_day", "value": "15:20"}]
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate intraday scalping put strategies: {e}")
            return []

    async def _generate_gamma_scalping_long_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate gamma scalping long strategies"""
        try:
            strategies = []

            # Get ATM strikes for gamma scalping
            atm_strikes = self.calculators.get_atm_strikes(option_chain, spot_price)

            for strike in atm_strikes[:2]:  # Limit to top 2 ATM strikes
                # Create straddle for gamma scalping
                call = option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                ).first()

                put = option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                ).first()

                if call is None or put is None:
                    continue

                call_dict = call.to_dict()
                put_dict = put.to_dict()

                # Create gamma scalping legs (long straddle)
                call_leg = OptionsLeg(
                    symbol=f"{underlying}{call_dict['expiry_date'].replace('-', '')}{int(strike)}CE",
                    option_type='CE',
                    strike_price=strike,
                    expiry_date=call_dict['expiry_date'],
                    quantity=1,
                    premium=call_dict['ltp'],
                    underlying=underlying
                )

                put_leg = OptionsLeg(
                    symbol=f"{underlying}{put_dict['expiry_date'].replace('-', '')}{int(strike)}PE",
                    option_type='PE',
                    strike_price=strike,
                    expiry_date=put_dict['expiry_date'],
                    quantity=1,
                    premium=put_dict['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics
                total_premium = call_dict['ltp'] + put_dict['ltp']
                max_profit = float('inf')  # Unlimited profit potential
                max_loss = total_premium
                break_even_upper = strike + total_premium
                break_even_lower = strike - total_premium
                prob_profit = 0.4  # Needs significant movement

                strategy = OptionsStrategy(
                    strategy_id=f"GSL_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.GAMMA_SCALPING_LONG,
                    underlying=underlying,
                    legs=[call_leg, put_leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even_lower, break_even_upper],
                    probability_of_profit=prob_profit,
                    net_premium=-total_premium,
                    margin_required=total_premium,
                    risk_reward_ratio=float('inf'),
                    target_profit=max_loss * 0.5,  # 50% profit target
                    stop_loss=max_loss * 0.3,     # 30% stop loss
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Volatility", "operator": "low", "value": 0.15}],
                    exit_conditions=[{"indicator": "Volatility", "operator": "expansion", "value": 0.25}]
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate gamma scalping long strategies: {e}")
            return []

    async def _generate_volatility_breakout_long_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate volatility breakout long strategies"""
        try:
            strategies = []

            # Get ATM strikes for volatility breakout
            atm_strikes = self.calculators.get_atm_strikes(option_chain, spot_price)

            for strike in atm_strikes[:2]:  # Limit to top 2 ATM strikes
                # Create long straddle for volatility breakout
                call = option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                ).first()

                put = option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                ).first()

                if call is None or put is None:
                    continue

                call_dict = call.to_dict()
                put_dict = put.to_dict()

                # Create volatility breakout legs
                call_leg = OptionsLeg(
                    symbol=f"{underlying}{call_dict['expiry_date'].replace('-', '')}{int(strike)}CE",
                    option_type='CE',
                    strike_price=strike,
                    expiry_date=call_dict['expiry_date'],
                    quantity=1,
                    premium=call_dict['ltp'],
                    underlying=underlying
                )

                put_leg = OptionsLeg(
                    symbol=f"{underlying}{put_dict['expiry_date'].replace('-', '')}{int(strike)}PE",
                    option_type='PE',
                    strike_price=strike,
                    expiry_date=put_dict['expiry_date'],
                    quantity=1,
                    premium=put_dict['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics
                total_premium = call_dict['ltp'] + put_dict['ltp']
                max_profit = float('inf')  # Unlimited profit potential
                max_loss = total_premium
                break_even_upper = strike + total_premium
                break_even_lower = strike - total_premium
                prob_profit = 0.45  # Higher probability for breakout

                strategy = OptionsStrategy(
                    strategy_id=f"VBL_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.VOLATILITY_BREAKOUT_LONG,
                    underlying=underlying,
                    legs=[call_leg, put_leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even_lower, break_even_upper],
                    probability_of_profit=prob_profit,
                    net_premium=-total_premium,
                    margin_required=total_premium,
                    risk_reward_ratio=float('inf'),
                    target_profit=max_loss * 1.0,  # 100% profit target
                    stop_loss=max_loss * 0.4,     # 40% stop loss
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Volatility", "operator": "compression", "value": 0.12}],
                    exit_conditions=[{"indicator": "Breakout", "operator": "confirmed", "value": "either_direction"}]
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate volatility breakout long strategies: {e}")
            return []

    async def _generate_reverse_iron_condor_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate reverse iron condor strategies (net debit)"""
        return []

    async def _generate_reverse_iron_butterfly_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate reverse iron butterfly strategies (net debit)"""
        return []

    async def _generate_bull_call_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate bull call spread strategies (net debit)"""
        return []
    
    async def _generate_bear_put_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate bear put spread strategies (net debit)"""
        return []

    async def _generate_call_calendar_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate call calendar spread strategies (net debit)"""
        try:
            strategies = []
            calls = option_chain.filter(pl.col('option_type') == 'CE')

            # Group calls by strike price
            for strike in calls['strike_price'].unique().sort().to_list():
                strike_calls = calls.filter(pl.col('strike_price') == strike).sort('expiry_date')

                if strike_calls.height < 2:
                    continue

                # Iterate through possible combinations for short (near) and long (far) expiry calls
                for i in range(strike_calls.height):
                    short_call_row = strike_calls.row(i, named=True)
                    for j in range(i + 1, strike_calls.height):
                        long_call_row = strike_calls.row(j, named=True)

                        # Ensure short expiry is earlier than long expiry
                        if short_call_row['expiry_date'] >= long_call_row['expiry_date']:
                            continue

                        # Create legs
                        short_leg = OptionsLeg(
                            symbol=short_call_row['symbol'],
                            option_type='CE',
                            strike_price=strike,
                            expiry_date=short_call_row['expiry_date'],
                            quantity=-1,  # Short position
                            premium=short_call_row['ltp'],
                            underlying=underlying
                        )

                        long_leg = OptionsLeg(
                            symbol=long_call_row['symbol'],
                            option_type='CE',
                            strike_price=strike,
                            expiry_date=long_call_row['expiry_date'],
                            quantity=1,
                            premium=long_call_row['ltp'],
                            underlying=underlying
                        )

                        # Calculate strategy metrics
                        net_premium = long_call_row['ltp'] - short_call_row['ltp']
                        if net_premium <= 0:  # Must be a debit spread
                            continue

                        max_profit = float('inf')  # Theoretical unlimited profit if underlying moves significantly
                        max_loss = net_premium
                        break_even_upper = strike + net_premium
                        break_even_lower = strike - net_premium # Simplified, actual BE is complex for calendar

                        # Estimate probability of profit (simplified)
                        prob_profit = 0.5 # Calendar spreads profit from time decay and volatility

                        strategy = OptionsStrategy(
                            strategy_id=f"CCS_{underlying}_{strike}_{short_call_row['expiry_date']}_{long_call_row['expiry_date']}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                            strategy_type=StrategyType.CALL_CALENDAR_SPREAD,
                            underlying=underlying,
                            legs=[short_leg, long_leg],
                            max_profit=max_profit,
                            max_loss=max_loss,
                            break_even_points=[break_even_lower, break_even_upper],
                            probability_of_profit=prob_profit,
                            net_premium=-net_premium,  # Negative for debit
                            margin_required=max_loss,  # Margin is max loss for debit spreads
                            risk_reward_ratio=float('inf'),
                            target_profit=max_loss * 1.5,
                            stop_loss=max_loss * 0.75,
                            created_at=datetime.now(),
                            entry_conditions=[{"indicator": "IV", "operator": "low_for_near_term", "value": 0.1}],
                            exit_conditions=[{"indicator": "Time", "operator": "near_short_expiry"}]
                        )
                        strategies.append(strategy)
            return strategies
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate call calendar spread strategies: {e}")
            return []

    async def _generate_put_calendar_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate put calendar spread strategies (net debit)"""
        try:
            strategies = []
            puts = option_chain.filter(pl.col('option_type') == 'PE')

            # Group puts by strike price
            for strike in puts['strike_price'].unique().sort().to_list():
                strike_puts = puts.filter(pl.col('strike_price') == strike).sort('expiry_date')

                if strike_puts.height < 2:
                    continue

                # Iterate through possible combinations for short (near) and long (far) expiry puts
                for i in range(strike_puts.height):
                    short_put_row = strike_puts.row(i, named=True)
                    for j in range(i + 1, strike_puts.height):
                        long_put_row = strike_puts.row(j, named=True)

                        # Ensure short expiry is earlier than long expiry
                        if short_put_row['expiry_date'] >= long_put_row['expiry_date']:
                            continue

                        # Create legs
                        short_leg = OptionsLeg(
                            symbol=short_put_row['symbol'],
                            option_type='PE',
                            strike_price=strike,
                            expiry_date=short_put_row['expiry_date'],
                            quantity=-1,  # Short position
                            premium=short_put_row['ltp'],
                            underlying=underlying
                        )

                        long_leg = OptionsLeg(
                            symbol=long_put_row['symbol'],
                            option_type='PE',
                            strike_price=strike,
                            expiry_date=long_put_row['expiry_date'],
                            quantity=1,
                            premium=long_put_row['ltp'],
                            underlying=underlying
                        )

                        # Calculate strategy metrics
                        net_premium = long_put_row['ltp'] - short_put_row['ltp']
                        if net_premium <= 0:  # Must be a debit spread
                            continue

                        max_profit = float('inf')  # Theoretical unlimited profit if underlying moves significantly
                        max_loss = net_premium
                        break_even_upper = strike + net_premium # Simplified, actual BE is complex for calendar
                        break_even_lower = strike - net_premium

                        # Estimate probability of profit (simplified)
                        prob_profit = 0.5 # Calendar spreads profit from time decay and volatility

                        strategy = OptionsStrategy(
                            strategy_id=f"PCS_{underlying}_{strike}_{short_put_row['expiry_date']}_{long_put_row['expiry_date']}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                            strategy_type=StrategyType.PUT_CALENDAR_SPREAD,
                            underlying=underlying,
                            legs=[short_leg, long_leg],
                            max_profit=max_profit,
                            max_loss=max_loss,
                            break_even_points=[break_even_lower, break_even_upper],
                            probability_of_profit=prob_profit,
                            net_premium=-net_premium,  # Negative for debit
                            margin_required=max_loss,  # Margin is max loss for debit spreads
                            risk_reward_ratio=float('inf'),
                            target_profit=max_loss * 1.5,
                            stop_loss=max_loss * 0.75,
                            created_at=datetime.now(),
                            entry_conditions=[{"indicator": "IV", "operator": "low_for_near_term", "value": 0.1}],
                            exit_conditions=[{"indicator": "Time", "operator": "near_short_expiry"}]
                        )
                        strategies.append(strategy)
            return strategies
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate put calendar spread strategies: {e}")
            return []

    async def _generate_diagonal_call_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate diagonal call spread strategies (net debit)"""
        try:
            strategies = []
            calls = option_chain.filter(pl.col('option_type') == 'CE').sort(['expiry_date', 'strike_price'])

            # Group calls by expiry date
            expiry_dates = calls['expiry_date'].unique().sort().to_list()

            if len(expiry_dates) < 2:
                return []

            for i in range(len(expiry_dates)):
                near_expiry = expiry_dates[i]
                near_calls = calls.filter(pl.col('expiry_date') == near_expiry)

                for j in range(i + 1, len(expiry_dates)):
                    far_expiry = expiry_dates[j]
                    far_calls = calls.filter(pl.col('expiry_date') == far_expiry)

                    # Iterate through combinations of strikes
                    for long_call_row in far_calls.iter_rows(named=True):
                        # Find a short call with a higher strike and near expiry
                        short_call_candidates = near_calls.filter(
                            pl.col('strike_price') > long_call_row['strike_price']
                        ).sort('strike_price')

                        if short_call_candidates.height == 0:
                            continue

                        # Pick the nearest OTM short call
                        short_call_row = short_call_candidates.row(0, named=True)

                        # Create legs
                        long_leg = OptionsLeg(
                            symbol=long_call_row['symbol'],
                            option_type='CE',
                            strike_price=long_call_row['strike_price'],
                            expiry_date=far_expiry,
                            quantity=1,
                            premium=long_call_row['ltp'],
                            underlying=underlying
                        )

                        short_leg = OptionsLeg(
                            symbol=short_call_row['symbol'],
                            option_type='CE',
                            strike_price=short_call_row['strike_price'],
                            expiry_date=near_expiry,
                            quantity=-1,  # Short position
                            premium=short_call_row['ltp'],
                            underlying=underlying
                        )

                        # Calculate strategy metrics
                        net_premium = long_call_row['ltp'] - short_call_row['ltp']
                        if net_premium <= 0:  # Must be a debit spread
                            continue

                        max_profit = float('inf')  # Theoretical unlimited profit
                        max_loss = net_premium
                        break_even = long_call_row['strike_price'] + net_premium # Simplified

                        # Estimate probability of profit (simplified)
                        prob_profit = 0.45 # Depends on directional bias and time decay

                        strategy = OptionsStrategy(
                            strategy_id=f"DCS_{underlying}_{long_call_row['strike_price']}_{short_call_row['strike_price']}_{near_expiry}_{far_expiry}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                            strategy_type=StrategyType.DIAGONAL_CALL_SPREAD,
                            underlying=underlying,
                            legs=[long_leg, short_leg],
                            max_profit=max_profit,
                            max_loss=max_loss,
                            break_even_points=[break_even],
                            probability_of_profit=prob_profit,
                            net_premium=-net_premium,  # Negative for debit
                            margin_required=max_loss,  # Margin is max loss for debit spreads
                            risk_reward_ratio=float('inf'),
                            target_profit=max_loss * 1.5,
                            stop_loss=max_loss * 0.75,
                            created_at=datetime.now(),
                            entry_conditions=[{"indicator": "Trend", "operator": "bullish", "value": "moderate"}],
                            exit_conditions=[{"indicator": "Time", "operator": "near_short_expiry"}]
                        )
                        strategies.append(strategy)
            return strategies
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate diagonal call spread strategies: {e}")
            return []

    async def _generate_diagonal_put_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate diagonal put spread strategies (net debit)"""
        try:
            strategies = []
            puts = option_chain.filter(pl.col('option_type') == 'PE').sort(['expiry_date', 'strike_price'], descending=[False, True])

            # Group puts by expiry date
            expiry_dates = puts['expiry_date'].unique().sort().to_list()

            if len(expiry_dates) < 2:
                return []

            for i in range(len(expiry_dates)):
                near_expiry = expiry_dates[i]
                near_puts = puts.filter(pl.col('expiry_date') == near_expiry)

                for j in range(i + 1, len(expiry_dates)):
                    far_expiry = expiry_dates[j]
                    far_puts = puts.filter(pl.col('expiry_date') == far_expiry)

                    # Iterate through combinations of strikes
                    for long_put_row in far_puts.iter_rows(named=True):
                        # Find a short put with a lower strike and near expiry
                        short_put_candidates = near_puts.filter(
                            pl.col('strike_price') < long_put_row['strike_price']
                        ).sort('strike_price', descending=True)

                        if short_put_candidates.height == 0:
                            continue

                        # Pick the nearest OTM short put
                        short_put_row = short_put_candidates.row(0, named=True)

                        # Create legs
                        long_leg = OptionsLeg(
                            symbol=long_put_row['symbol'],
                            option_type='PE',
                            strike_price=long_put_row['strike_price'],
                            expiry_date=far_expiry,
                            quantity=1,
                            premium=long_put_row['ltp'],
                            underlying=underlying
                        )

                        short_leg = OptionsLeg(
                            symbol=short_put_row['symbol'],
                            option_type='PE',
                            strike_price=short_put_row['strike_price'],
                            expiry_date=near_expiry,
                            quantity=-1,  # Short position
                            premium=short_put_row['ltp'],
                            underlying=underlying
                        )

                        # Calculate strategy metrics
                        net_premium = long_put_row['ltp'] - short_put_row['ltp']
                        if net_premium <= 0:  # Must be a debit spread
                            continue

                        max_profit = float('inf')  # Theoretical unlimited profit
                        max_loss = net_premium
                        break_even = long_put_row['strike_price'] - net_premium # Simplified

                        # Estimate probability of profit (simplified)
                        prob_profit = 0.45 # Depends on directional bias and time decay

                        strategy = OptionsStrategy(
                            strategy_id=f"DPS_{underlying}_{long_put_row['strike_price']}_{short_put_row['strike_price']}_{near_expiry}_{far_expiry}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                            strategy_type=StrategyType.DIAGONAL_PUT_SPREAD,
                            underlying=underlying,
                            legs=[long_leg, short_leg],
                            max_profit=max_profit,
                            max_loss=max_loss,
                            break_even_points=[break_even],
                            probability_of_profit=prob_profit,
                            net_premium=-net_premium,  # Negative for debit
                            margin_required=max_loss,  # Margin is max loss for debit spreads
                            risk_reward_ratio=float('inf'),
                            target_profit=max_loss * 1.5,
                            stop_loss=max_loss * 0.75,
                            created_at=datetime.now(),
                            entry_conditions=[{"indicator": "Trend", "operator": "bearish", "value": "moderate"}],
                            exit_conditions=[{"indicator": "Time", "operator": "near_short_expiry"}]
                        )
                        strategies.append(strategy)
            return strategies
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate diagonal put spread strategies: {e}")
            return []

    async def _generate_ratio_call_backspread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate ratio call backspread strategies (net debit)"""
        return []

    async def _generate_ratio_put_backspread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate ratio put backspread strategies (net debit)"""
        return []

    async def _generate_collar_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate collar strategies (often net zero or small debit)"""
        return []

    async def _generate_synthetic_long_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate synthetic long strategies"""
        return []

    async def _generate_synthetic_call_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate synthetic long call strategies"""
        return []

    async def _generate_synthetic_put_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate synthetic long put strategies"""
        return []

    async def _generate_butterfly_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate butterfly spread strategies (net debit)"""
        return []

    async def _generate_condor_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate condor spread strategies (net debit)"""
        return []

    async def _generate_christmas_tree_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate christmas tree strategies (net debit)"""
        return []

    async def _generate_weekly_expiry_straddle_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate weekly expiry straddle strategies"""
        return []

    async def _generate_monthly_expiry_strangle_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate monthly expiry strangle strategies"""
        return []

    async def _generate_banknifty_butterfly_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate BankNifty butterfly strategies (assuming net debit)"""
        return []
