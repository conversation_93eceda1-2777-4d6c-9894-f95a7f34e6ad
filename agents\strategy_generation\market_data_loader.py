import logging
import polars as pl
from pathlib import Path
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)

class MarketDataLoader:
    """Loads market data for strategy generation."""

    def __init__(self, data_path: Path):
        self.data_path = data_path
        self.option_chains_path = self.data_path / "option_chains"
        self.features_path = self.data_path / "features"

    async def load_market_data(self, underlying_symbols: List[str]) -> Dict[str, pl.DataFrame]:
        """Load current market data for strategy generation."""
        market_data_cache = {}
        try:
            logger.info("[LOAD] Loading market data...")
            
            for underlying in underlying_symbols:
                # Load latest option chain data
                chain_files = list(self.option_chains_path.glob(f"{underlying}_*_chain_*.parquet"))
                
                if chain_files:
                    latest_file = max(chain_files, key=lambda x: x.stat().st_mtime)
                    chain_data = pl.read_parquet(latest_file)
                    market_data_cache[underlying] = chain_data
                    logger.info(f"[LOAD] Loaded option chain for {underlying}: {chain_data.height} contracts")
                else:
                    logger.warning(f"[WARNING] No option chain data found for {underlying}")
            
            return market_data_cache
        except Exception as e:
            logger.error(f"[ERROR] Failed to load market data: {e}")
            return {}

    async def load_feature_data(self, underlying: str, timeframe: str) -> Optional[pl.DataFrame]:
        """Load feature data for strategy generation."""
        try:
            feature_path = self.features_path / timeframe
            
            # Look for feature files
            patterns = [
                f"feature_{underlying}_{timeframe}_*.parquet",
                f"feature_{underlying}_*.parquet",
                f"*{underlying}*feature*.parquet"
            ]
            
            files = []
            for pattern in patterns:
                files.extend(list(feature_path.glob(pattern)))
            
            if not files:
                logger.warning(f"[LOAD] No feature data found for {underlying} {timeframe}")
                return None
            
            # Load the most recent file
            latest_file = max(files, key=lambda x: x.stat().st_mtime)
            df = pl.read_parquet(latest_file)
            
            logger.info(f"[LOAD] Loaded {df.height} feature records for {underlying} {timeframe}")
            return df
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to load feature data: {e}")
            return None
