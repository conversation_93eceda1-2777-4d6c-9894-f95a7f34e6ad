import logging
import polars as pl
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Union
from enum import Enum

# Options pricing
try:
    import py_vollib.black_scholes as bs
    import py_vollib.black_scholes.greeks.analytical as greeks
except ImportError:
    logging.warning("py_vollib not installed. Using fallback calculations.")

# Import StrategyType after logger setup to avoid circular imports
from agents.strategy_generation.data_models import StrategyType

logger = logging.getLogger(__name__)

class StrategyCalculators:
    """
    Provides methods for calculating various metrics for options strategies.
    """

    def __init__(self, risk_free_rate: float = 0.06):
        self.risk_free_rate = risk_free_rate
        logger.info("[INIT] StrategyCalculators initialized")

    def calculate_max_profit_loss(self, legs: List[Dict], spot_price: float) -> Tuple[float, float]:
        """
        Calculate maximum profit and maximum loss for a given strategy.
        This is a simplified calculation and may need to be more robust for complex strategies.
        """
        max_profit = float('-inf')
        max_loss = float('inf')
        net_premium = sum(leg['premium'] * leg['quantity'] for leg in legs)

        # For simplicity, assume max profit/loss is at 0 or infinity for single legs
        # For spreads, it's typically capped.
        if len(legs) == 1:
            leg = legs[0]
            if leg['option_type'] == 'CE':
                if leg['quantity'] > 0:  # Long Call
                    max_profit = float('inf')
                    max_loss = leg['premium']
            elif leg['option_type'] == 'PE':
                if leg['quantity'] > 0:  # Long Put
                    max_profit = leg['strike_price'] - leg['premium'] # Max profit at 0
                    max_loss = leg['premium']
        else:
            # For multi-leg strategies, a more detailed payoff analysis is needed.
            # This is a placeholder for a more sophisticated calculation.
            # For now, we'll use a simplified approach based on net premium for net debit strategies.
            max_profit = float('inf') # Potentially unlimited profit for long options
            max_loss = abs(net_premium)

        return max_profit, max_loss

    def calculate_break_even_points(self, legs: List[Dict], spot_price: float) -> List[float]:
        """
        Calculate break-even points for a given strategy.
        This is a simplified calculation and may need to be more robust for complex strategies.
        """
        net_premium = sum(leg['premium'] * leg['quantity'] for leg in legs)
        break_evens = []

        if len(legs) == 1:
            leg = legs[0]
            if leg['option_type'] == 'CE':
                if leg['quantity'] > 0:  # Long Call
                    break_evens.append(leg['strike_price'] + leg['premium'])
            elif leg['option_type'] == 'PE':
                if leg['quantity'] > 0:  # Long Put
                    break_evens.append(leg['strike_price'] - leg['premium'])
        elif len(legs) == 2: # Simple spreads
            # This is a very basic example for a two-leg spread.
            # A more robust solution would involve iterating through a range of underlying prices
            # and calculating the P&L at each point to find where P&L is zero.
            pass # Placeholder for now

        return break_evens

    def estimate_probability_of_profit(self, strategy_type: Enum, break_even_points: List[float],
                                        spot_price: float, implied_volatility: float,
                                        time_to_expiry_years: float) -> float:
        """
        Estimate probability of profit using Black-Scholes or simplified methods.
        This is a highly simplified estimation.
        """
        if not break_even_points:
            return 0.0

        # For single-leg options, a simplified approach
        if strategy_type == StrategyType.LONG_CALL:
            # Probability of call expiring ITM
            d2 = (np.log(spot_price / break_even_points[0]) + (self.risk_free_rate - 0.5 * implied_volatility**2) * time_to_expiry_years) / (implied_volatility * np.sqrt(time_to_expiry_years))
            prob_itm = self._norm_cdf(d2)
            return prob_itm
        elif strategy_type == StrategyType.LONG_PUT:
            # Probability of put expiring ITM
            d2 = (np.log(spot_price / break_even_points[0]) + (self.risk_free_rate - 0.5 * implied_volatility**2) * time_to_expiry_years) / (implied_volatility * np.sqrt(time_to_expiry_years))
            prob_itm = self._norm_cdf(-d2)
            return prob_itm
        
        # For other strategies, a very rough estimate
        if strategy_type in [StrategyType.LONG_STRADDLE, StrategyType.LONG_STRANGLE]:
            return 0.35 # Needs significant move
        
        return 0.5 # Default if not specifically handled

    def _norm_cdf(self, x: float) -> float:
        """Standard normal cumulative distribution function."""
        return (1.0 + erf(x / np.sqrt(2.0))) / 2.0

    def calculate_net_premium(self, legs: List[Dict]) -> float:
        """Calculate net premium for the strategy."""
        return sum(leg['premium'] * leg['quantity'] for leg in legs)

    def calculate_margin_required(self, legs: List[Dict]) -> float:
        """
        Estimate margin required for the strategy.
        This is a simplified estimation and actual margin depends on broker rules.
        """
        # For long options, margin is typically the premium paid.
        total_margin = 0.0
        for leg in legs:
            # Only long legs are considered for margin calculation as we are option buyers only
            total_margin += abs(leg['premium'])
        return total_margin

    def calculate_risk_reward_ratio(self, max_profit: float, max_loss: float) -> float:
        """Calculate risk-reward ratio."""
        if max_loss == 0:
            return float('inf')
        if max_profit == float('inf') and max_loss == float('inf'):
            return 1.0 # Undefined, but for practical purposes
        if max_profit == float('inf'):
            return float('inf')
        if max_loss == float('inf'):
            return 0.0
        return abs(max_profit / max_loss)

    def get_atm_strikes(self, option_chain: pl.DataFrame, spot_price: float) -> List[float]:
        """Get At-The-Money strike prices closest to spot price."""
        try:
            # Get unique strikes and find closest to spot price
            strikes = option_chain['strike_price'].unique().sort().to_list()

            if not strikes:
                return []

            # Find the strike closest to spot price
            closest_strike = min(strikes, key=lambda x: abs(x - spot_price))

            # Return ATM strikes (closest strike and adjacent ones)
            atm_strikes = []
            closest_idx = strikes.index(closest_strike)

            # Add the closest strike
            atm_strikes.append(closest_strike)

            # Add adjacent strikes if they exist
            if closest_idx > 0:
                atm_strikes.append(strikes[closest_idx - 1])
            if closest_idx < len(strikes) - 1:
                atm_strikes.append(strikes[closest_idx + 1])

            return sorted(atm_strikes)

        except Exception as e:
            logger.error(f"[ERROR] Failed to get ATM strikes: {e}")
            return []

    def get_otm_strikes(self, option_chain: pl.DataFrame, spot_price: float, option_type: str) -> List[float]:
        """Get Out-of-The-Money strike prices."""
        try:
            # Get unique strikes
            strikes = option_chain['strike_price'].unique().sort().to_list()

            if not strikes:
                return []

            otm_strikes = []

            if option_type == 'CE':  # Call options - OTM strikes are above spot price
                otm_strikes = [strike for strike in strikes if strike > spot_price]
            elif option_type == 'PE':  # Put options - OTM strikes are below spot price
                otm_strikes = [strike for strike in strikes if strike < spot_price]

            return sorted(otm_strikes)

        except Exception as e:
            logger.error(f"[ERROR] Failed to get OTM strikes for {option_type}: {e}")
            return []

    def get_itm_strikes(self, option_chain: pl.DataFrame, spot_price: float, option_type: str) -> List[float]:
        """Get In-The-Money strike prices."""
        try:
            # Get unique strikes
            strikes = option_chain['strike_price'].unique().sort().to_list()

            if not strikes:
                return []

            itm_strikes = []

            if option_type == 'CE':  # Call options - ITM strikes are below spot price
                itm_strikes = [strike for strike in strikes if strike < spot_price]
            elif option_type == 'PE':  # Put options - ITM strikes are above spot price
                itm_strikes = [strike for strike in strikes if strike > spot_price]

            return sorted(itm_strikes)

        except Exception as e:
            logger.error(f"[ERROR] Failed to get ITM strikes for {option_type}: {e}")
            return []

# Helper for _norm_cdf
from math import erf
