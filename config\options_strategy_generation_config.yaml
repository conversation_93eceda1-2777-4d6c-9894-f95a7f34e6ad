# Options Strategy Generation Agent Configuration

# Basic Configuration
agent_name: "Options Strategy Generation Agent"
version: "1.0.0"
description: "Dynamic options strategy generation and optimization"

# Market Configuration
underlying_symbols:
  - "NIFTY"
  - "BANKNIFTY"
  - "FINNIFTY"

# Strategy Generation Parameters
strategy_generation:
  max_strategies_per_underlying: 50
  max_legs_per_strategy: 4
  min_probability_of_profit: 0.3
  max_risk_per_strategy: 10000.0
  min_risk_reward_ratio: 1.5

# Risk Management
risk_management:
  max_position_size: 100000.0
  max_daily_loss: 50000.0
  max_portfolio_risk: 200000.0
  position_sizing_method: "fixed_amount"

# Market Data Configuration
market_data:
  data_source: "local_files"
  update_frequency: "1min"
  lookback_days: 30
  min_volume_threshold: 1000
  min_open_interest_threshold: 100

# Strategy Filtering
filtering:
  min_volume: 1000
  min_open_interest: 100
  max_bid_ask_spread: 0.05
  min_time_to_expiry_days: 1
  max_time_to_expiry_days: 45

# Optimization Parameters
optimization:
  objective: "risk_adjusted_return"
  max_iterations: 100
  convergence_threshold: 0.001
  population_size: 50

# Entry/Exit Logic Configuration
entry_exit_logic:
  default_entry_conditions:
    - indicator: "Volume"
      operator: ">"
      value: 10000
    - indicator: "OpenInterest"
      operator: ">"
      value: 1000
  
  default_exit_conditions:
    - indicator: "ProfitTarget"
      operator: ">="
      value: 0.5  # 50% of max profit
    - indicator: "StopLoss"
      operator: "<="
      value: -0.3  # 30% of premium paid
    - indicator: "TimeDecay"
      operator: "end_of_day"
      value: null

# Greeks Thresholds
greeks:
  max_delta: 0.8
  max_gamma: 0.1
  max_theta: -50
  max_vega: 100
  max_rho: 10

# Volatility Parameters
volatility:
  min_implied_volatility: 0.1
  max_implied_volatility: 1.0
  volatility_smile_adjustment: true
  use_historical_volatility: true

# Logging Configuration
logging:
  level: "INFO"
  file_path: "logs/strategy_generation.log"
  max_file_size: "10MB"
  backup_count: 5

# Performance Configuration
performance:
  enable_parallel_processing: true
  max_workers: 4
  chunk_size: 1000
  memory_limit_mb: 2048

# Output Configuration
output:
  save_format: ["json", "parquet"]
  output_directory: "data/strategies"
  include_metadata: true
  compress_output: true
